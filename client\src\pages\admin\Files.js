import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Upload, 
  message, 
  Typography, 
  Spin, 
  Tag, 
  Popconfirm,
  Tooltip,
  Alert,
  Divider,
  App,
  Switch,
  Image
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  FileOutlined,
  FileTextOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileZipOutlined,
  ReloadOutlined,
  EditOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getAdminFiles } from '../../store/slices/adminSlice';
import { getCategories } from '../../store/slices/categorySlice';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 文件图标映射
const getFileIcon = (fileType) => {
  switch (fileType?.toLowerCase()) {
    case 'pdf':
      return <FilePdfOutlined style={{ fontSize: 20, color: '#ff4d4f' }} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return <FileImageOutlined style={{ fontSize: 20, color: '#52c41a' }} />;
    case 'doc':
    case 'docx':
    case 'txt':
      return <FileTextOutlined style={{ fontSize: 20, color: '#1890ff' }} />;
    case 'zip':
    case 'rar':
    case '7z':
      return <FileZipOutlined style={{ fontSize: 20, color: '#faad14' }} />;
    default:
      return <FileOutlined style={{ fontSize: 20, color: '#722ed1' }} />;
  }
};

// 格式化文件大小
const formatFileSize = (sizeInKB) => {
  // 如果文件大小为null或0（网盘链接类资料），不显示大小
  if (sizeInKB === null || sizeInKB === 0) {
    return '无文件';
  }
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`;
  } else {
    return `${(sizeInKB / 1024).toFixed(2)} MB`;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 自定义图片组件，支持身份验证
const AuthenticatedImage = ({ src, ...props }) => {
  const [imageSrc, setImageSrc] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        // 检查是否是外部链接（如闲鱼图片）
        if (src.startsWith('http://') || src.startsWith('https://')) {
          // 外部链接直接使用
          setImageSrc(src);
          setLoading(false);
          return;
        }

        // 本地图片通过API加载
        const adminToken = localStorage.getItem('adminToken');
        const response = await axios.get(src, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          },
          responseType: 'blob'
        });

        const imageUrl = URL.createObjectURL(response.data);
        setImageSrc(imageUrl);
        setLoading(false);
      } catch (error) {
        console.error('加载图片失败:', error);
        setLoading(false);
      }
    };

    if (src) {
      loadImage();
    }

    return () => {
      if (imageSrc && !imageSrc.startsWith('http')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src]);

  if (loading) {
    return <div style={{ ...props.style, display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5' }}>加载中...</div>;
  }

  return <img src={imageSrc} alt="简介图片" {...props} />;
};

const Files = () => {
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [previewImageList, setPreviewImageList] = useState([]);
  const [editPreviewImageList, setEditPreviewImageList] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isTextOnly, setIsTextOnly] = useState(false);
  const { message: messageApi } = App.useApp();
  
  // 新增的状态变量
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingFile, setEditingFile] = useState(null);
  const [editForm] = Form.useForm();

  // 闲鱼同步相关状态
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [syncForm] = Form.useForm();
  const [xianyuSettings, setXianyuSettings] = useState({
    cookies: '',
    has_cookies: false,
    max_items: 50
  });

  const dispatch = useDispatch();
  const { files, totalFiles, filePage, fileLimit, loadingFiles } = useSelector((state) => state.admin);
  const { categories, loading: loadingCategories } = useSelector((state) => state.category);

  // 加载文件和分类
  useEffect(() => {
    dispatch(getCategories());
    dispatch(getAdminFiles({ page: currentPage, limit: pageSize }));
  }, [dispatch, currentPage, pageSize]);

  // 处理文件上传
  const handleFileUpload = async (values) => {
    console.log('开始文件上传流程');
    console.log('fileList:', fileList);
    
    try {
      // 检查是否是纯文本模式
      if (isTextOnly) {
        if (!values.important_text || !values.important_text.trim()) {
          messageApi.error('请输入网盘链接');
          return;
        }
      } else if (fileList.length === 0) {
        messageApi.error('请选择要上传的文件');
        return;
      }

      // 获取管理员令牌
      const adminToken = localStorage.getItem('adminToken');
      if (!adminToken) {
        messageApi.error('管理员身份验证失败，请重新登录');
        return;
      }

      const formData = new FormData();
      
      // 如果不是纯文本模式，添加文件
      if (!isTextOnly && fileList.length > 0) {
        // 获取原始文件对象
        const file = fileList[0].originFileObj || fileList[0].file || fileList[0];
        formData.append('file', file);
      }

      // 添加简介图片
      if (previewImageList.length > 0) {
        previewImageList.forEach(item => {
          const file = item.originFileObj || item.file || item;
          formData.append('preview_images', file);
        });
      }

      // 添加描述和网盘链接
      formData.append('description', values.description || '');
      formData.append('important_text', values.important_text || '');
      
      // 设置分类ID
      if (values.category_id) {
        formData.append('category_id', values.category_id);
      }
      
      // 设置自定义文件名（如果有）
      if (values.filename && values.filename.trim()) {
        let customFilename = values.filename.trim();
        
        // 如果有文件，可能需要添加扩展名
        if (!isTextOnly && fileList.length > 0) {
          const file = fileList[0].originFileObj || fileList[0].file || fileList[0];
          const originalExt = file.name.split('.').pop();
          
          if (!customFilename.includes('.') && originalExt) {
            customFilename = `${customFilename}.${originalExt}`;
          }
        }
        
        console.log('设置自定义文件名:', customFilename);
        formData.append('filename', customFilename);
      } else if (!isTextOnly && fileList.length > 0) {
        // 使用原始文件名
        const file = fileList[0].originFileObj || fileList[0].file || fileList[0];
        console.log('使用原始文件名:', file.name);
        formData.append('filename', file.name);
      }

      // 调试信息
      console.log('准备发送请求到URL:', '/api/admin/files/upload');
      console.log('是否为纯文本上传:', isTextOnly);
      
      const response = await axios.post('/api/admin/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${adminToken}`
        }
      });

      console.log('上传响应:', response.data);
      messageApi.success(isTextOnly ? '网盘链接上传成功' : '文件上传成功');
      setUploadModalVisible(false);
      uploadForm.resetFields();
      setFileList([]);
      setPreviewImageList([]);
      setIsTextOnly(false);
      
      // 重新加载文件列表
      dispatch(getAdminFiles({ page: currentPage, limit: pageSize }));
    } catch (error) {
      console.error('上传文件错误:', error);
      messageApi.error(error.response?.data?.message || '上传失败，请重试');
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1);
    dispatch(getAdminFiles({
      page: 1,
      limit: pageSize,
      keyword: searchKeyword,
      category_id: selectedCategoryId
    }));
  };

  // 处理重置筛选
  const handleResetFilter = () => {
    setSearchKeyword('');
    setSelectedCategoryId(null);
    setCurrentPage(1);
    dispatch(getAdminFiles({ page: 1, limit: pageSize }));
  };

  // 处理分页变化
  const handlePageChange = (newPage, newPageSize) => {
    setCurrentPage(newPage);
    setPageSize(newPageSize);
    dispatch(getAdminFiles({
      page: newPage,
      limit: newPageSize,
      keyword: searchKeyword,
      category_id: selectedCategoryId
    }));
  };

  // 处理预览
  const handlePreview = async (file) => {
    try {
      // 先获取完整的文件信息
      const response = await axios.get(`/api/admin/files/${file.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.data && response.data.success) {
        // 使用服务器返回的完整文件信息
        setPreviewFile(response.data.data.file);
        setPreviewModalVisible(true);
      } else {
        messageApi.error('获取文件信息失败');
      }
    } catch (error) {
      console.error('获取文件详情错误:', error);
      messageApi.error('获取文件信息失败，请重试');
      // 如果获取失败，则使用当前行的数据
      setPreviewFile(file);
      setPreviewModalVisible(true);
    }
  };

  // 处理编辑
  const handleEdit = async (file) => {
    try {
      // 先获取完整的文件信息
      const response = await axios.get(`/api/admin/files/${file.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.data && response.data.success) {
        // 使用服务器返回的完整文件信息
        const fileData = response.data.data.file;
        setEditingFile(fileData);
        setFileList([]); // 清空文件列表

        // 处理现有简介图片
        const existingPreviewImages = [];
        if (fileData.preview_images_parsed && fileData.preview_images_parsed.length > 0) {
          fileData.preview_images_parsed.forEach((imagePath, index) => {
            existingPreviewImages.push({
              uid: `existing-${index}`,
              name: imagePath,
              status: 'done',
              url: imagePath.startsWith('http') ? imagePath : `/api/admin/preview/${imagePath}`,
              isExisting: true
            });
          });
        }
        setEditPreviewImageList(existingPreviewImages);

        editForm.setFieldsValue({
          file_name: fileData.file_name,
          description: fileData.description || '',
          category_id: fileData.category_id || undefined,
          important_text: fileData.important_text || '',
          status: fileData.status
        });
        setEditModalVisible(true);
      } else {
        messageApi.error('获取文件信息失败');
      }
    } catch (error) {
      console.error('获取文件详情错误:', error);
      messageApi.error('获取文件信息失败，请重试');
      // 如果获取失败，则使用当前行的数据
      setEditingFile(file);
      setFileList([]); // 清空文件列表

      // 处理现有简介图片（如果有的话）
      const existingPreviewImages = [];
      if (file.preview_images_parsed && file.preview_images_parsed.length > 0) {
        file.preview_images_parsed.forEach((imagePath, index) => {
          existingPreviewImages.push({
            uid: `existing-${index}`,
            name: imagePath,
            status: 'done',
            url: imagePath.startsWith('http') ? imagePath : `/api/admin/preview/${imagePath}`,
            isExisting: true
          });
        });
      }
      setEditPreviewImageList(existingPreviewImages);

      editForm.setFieldsValue({
        file_name: file.file_name,
        description: file.description || '',
        category_id: file.category_id || undefined,
        important_text: file.important_text || '',
        status: file.status
      });
      setEditModalVisible(true);
    }
  };

  // 获取闲鱼设置
  const fetchXianyuSettings = async () => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const response = await axios.get('/api/admin/xianyu-settings', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (response.data.success) {
        setXianyuSettings(response.data.data);
        syncForm.setFieldsValue({
          max_items: response.data.data.max_items
        });
      }
    } catch (error) {
      console.error('获取闲鱼设置失败:', error);
    }
  };

  // 更新闲鱼设置
  const updateXianyuSettings = async (cookies, maxItems) => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      const response = await axios.put('/api/admin/xianyu-settings', {
        cookies: cookies,
        max_items: maxItems
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (response.data.success) {
        messageApi.success('设置保存成功');
        await fetchXianyuSettings(); // 重新获取设置
      }
    } catch (error) {
      console.error('更新闲鱼设置失败:', error);
      messageApi.error('设置保存失败');
    }
  };

  // 处理闲鱼商品同步
  const handleSyncXianyu = async (values) => {
    try {
      setSyncing(true);
      const adminToken = localStorage.getItem('adminToken');

      // 如果提供了新的cookies，先保存设置
      if (values.cookies_str) {
        await updateXianyuSettings(values.cookies_str, xianyuSettings.max_items);
      }

      const response = await axios.post('/api/admin/sync-xianyu', {
        cookies_str: values.cookies_str
        // 移除max_items参数，让后端同步所有商品
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (response.data.success) {
        messageApi.success(response.data.message);
        setSyncModalVisible(false);
        syncForm.resetFields();

        // 重新加载文件列表
        dispatch(getAdminFiles({
          page: currentPage,
          limit: pageSize,
          keyword: searchKeyword,
          category_id: selectedCategoryId
        }));

        // 显示同步结果详情
        if (response.data.data.errors && response.data.data.errors.length > 0) {
          Modal.info({
            title: '同步完成',
            content: (
              <div>
                <p>成功同步 {response.data.data.saved} 个商品</p>
                {response.data.data.errors.length > 0 && (
                  <div>
                    <p>以下商品处理时出现问题：</p>
                    <ul>
                      {response.data.data.errors.slice(0, 5).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {response.data.data.errors.length > 5 && (
                        <li>...还有 {response.data.data.errors.length - 5} 个错误</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            ),
            width: 600
          });
        }
      } else {
        messageApi.error(response.data.message || '同步失败');
      }
    } catch (error) {
      console.error('同步闲鱼商品失败:', error);
      messageApi.error(error.response?.data?.message || '同步失败，请重试');
    } finally {
      setSyncing(false);
    }
  };

  // 提交编辑表单
  const handleEditSubmit = async (values) => {
    try {
      const adminToken = localStorage.getItem('adminToken');
      let url = `/api/admin/files/${editingFile.id}`;
      let data = values;
      let headers = {
        'Authorization': `Bearer ${adminToken}`
      };
      let method = 'put';

      // 检查是否有简介图片更新
      const hasPreviewImageChanges = editPreviewImageList.some(item => !item.isExisting);
      const hasNewFile = fileList.length > 0 && !(editingFile.file_type === 'text' && !editingFile.file_path);
      const hasExistingImages = editPreviewImageList.some(item => item.isExisting);

      // 如果有新文件上传、简介图片更新，或者需要保留现有图片，使用FormData
      if (hasNewFile || hasPreviewImageChanges || hasExistingImages) {
        const formData = new FormData();

        // 如果有新文件
        if (hasNewFile) {
          const file = fileList[0].originFileObj || fileList[0];
          formData.append('file', file);
          url = `/api/admin/files/${editingFile.id}/update-file`;
        }

        // 添加基本字段
        formData.append('file_name', values.file_name);
        formData.append('description', values.description || '');
        formData.append('category_id', values.category_id || '');
        formData.append('important_text', values.important_text || '');
        formData.append('status', values.status);

        // 处理简介图片
        // 添加新的简介图片
        editPreviewImageList.forEach((item) => {
          if (!item.isExisting && item.originFileObj) {
            formData.append('preview_images', item.originFileObj);
          }
        });

        // 如果有保留的现有图片，设置保留标志
        if (hasExistingImages) {
          formData.append('keep_preview_images', 'true');
        }

        data = formData;
        headers = {
          ...headers,
          'Content-Type': 'multipart/form-data'
        };
      }
      
      // 发送请求
      const response = await axios({
        method,
        url,
        data,
        headers
      });
      
      messageApi.success('文件信息更新成功');
      setEditModalVisible(false);
      setFileList([]);
      setEditPreviewImageList([]);
      
      // 重新加载文件列表
      dispatch(getAdminFiles({
        page: currentPage,
        limit: pageSize,
        keyword: searchKeyword,
        category_id: selectedCategoryId
      }));
    } catch (error) {
      console.error('更新文件错误:', error);
      messageApi.error(error.response?.data?.message || '更新失败，请重试');
    }
  };

  // 编辑时的上传前检查
  const beforeEditUpload = (file) => {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      messageApi.error('文件大小不能超过50MB!');
    }
    return isLt50M;
  };
  
  // 编辑时的上传配置
  const editUploadProps = {
    name: 'file',
    beforeUpload: beforeEditUpload,
    fileList,
    onChange({ fileList }) {
      setFileList(fileList);
    },
    customRequest: ({ file, onSuccess }) => {
      // 不执行实际上传，只是将文件添加到fileList中
      setTimeout(() => {
        onSuccess("ok", null);
      }, 0);
    },
    action: null,
    multiple: false,
    maxCount: 1
  };

  // 获取预览内容
  const renderPreviewContent = (file) => {
    // 基本文件信息部分（所有文件类型都显示）
    const fileInfoSection = (
      <div className="file-basic-info" style={{ marginBottom: '20px', padding: '15px', background: '#f9f9f9', borderRadius: '4px' }}>
        <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px' }}>文件基本信息</h3>
        <p><strong>文件名称：</strong>{file.file_name}</p>
        <p><strong>文件类型：</strong>{file.file_type?.toUpperCase()}</p>
        {/* 对于纯文本类型不显示文件大小 */}
        {!(file.file_type === 'text' && !file.file_path) && file.file_size > 0 && (
          <p><strong>文件大小：</strong>{formatFileSize(file.file_size)}</p>
        )}
        <p><strong>上传时间：</strong>{formatDate(file.upload_time)}</p>
        <p><strong>下载次数：</strong>{file.download_count || 0}</p>
        {file.Category?.name && <p><strong>所属分类：</strong>{file.Category.name}</p>}
        <p><strong>状态：</strong><Tag color={file.status ? 'green' : 'red'}>{file.status ? '可用' : '不可用'}</Tag></p>
        {file.description && (
          <div style={{ marginTop: '10px' }}>
            <strong>文件描述：</strong>
            <div style={{ 
              padding: '10px', 
              background: '#fff', 
              border: '1px solid #eee', 
              borderRadius: '4px',
              marginTop: '5px',
              whiteSpace: 'pre-wrap'
            }}>
              {file.description}
            </div>
          </div>
        )}
      </div>
    );

    // 简介图片显示组件
    const previewImagesSection = file.preview_images_parsed && file.preview_images_parsed.length > 0 ? (
      <div style={{ marginTop: '20px' }}>
        <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px' }}>简介图片</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
          {file.preview_images_parsed.map((imagePath, index) => (
            <AuthenticatedImage
              key={index}
              width={120}
              height={120}
              src={imagePath.startsWith('http') ? imagePath : `/api/admin/preview/${imagePath}`}
              style={{ objectFit: 'cover', borderRadius: 4, border: '1px solid #eee', width: 120, height: 120 }}
            />
          ))}
        </div>
      </div>
    ) : null;

    // 纯文本文件，显示网盘链接和基本信息
    if (file.file_type === 'text' && !file.file_path) {
      return (
        <div>
          {fileInfoSection}
          {previewImagesSection}
          <div style={{ marginTop: '20px' }}>
            <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px' }}>网盘链接</h3>
            <div style={{
              background: '#f5f5f5',
              padding: '20px',
              borderRadius: '4px',
              whiteSpace: 'pre-wrap',
              overflowWrap: 'break-word',
              border: '1px solid #e8e8e8'
            }}>
              {file.important_text || '无内容'}
            </div>
          </div>
        </div>
      );
    }

    // 图片文件
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    if (imageTypes.includes(file.file_type?.toLowerCase())) {
      return (
        <div>
          {fileInfoSection}
          {previewImagesSection}
          <div style={{ marginTop: '20px', textAlign: 'center' }}>
            <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px', textAlign: 'left' }}>图片预览</h3>
            <div style={{ maxWidth: '100%', overflow: 'hidden' }}>
              <Image
                src={`/api/files/${file.id}/download`}
                alt={file.file_name}
                style={{ maxWidth: '100%', maxHeight: '500px', objectFit: 'contain' }}
              />
            </div>
            <div style={{ marginTop: '15px' }}>
              <Button type="primary" href={`/api/files/${file.id}/download?download=true`} target="_blank">
                下载原图
              </Button>
            </div>
          </div>
          {file.important_text && (
            <div style={{ marginTop: '20px' }}>
              <h4 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px' }}>相关网盘链接</h4>
              <div style={{ 
                background: '#f5f5f5', 
                padding: '15px', 
                borderRadius: '4px', 
                whiteSpace: 'pre-wrap',
                overflowWrap: 'break-word',
                border: '1px solid #e8e8e8'
              }}>
                {file.important_text}
              </div>
            </div>
          )}
        </div>
      );
    }

    // PDF 文件
    if (file.file_type?.toLowerCase() === 'pdf') {
      return (
        <div>
          {fileInfoSection}
          {previewImagesSection}
          <div style={{ marginTop: '20px' }}>
            <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px' }}>PDF预览</h3>
            <div style={{ textAlign: 'center', maxWidth: '100%', overflow: 'hidden' }}>
              <iframe 
                src={`/api/files/${file.id}/download`} 
                width="100%" 
                height="500px" 
                title="PDF预览"
                style={{ border: 'none', maxWidth: '100%' }}
              />
              <div style={{ marginTop: '15px' }}>
                <Button type="primary" href={`/api/files/${file.id}/download?download=true`} target="_blank">
                  下载PDF
                </Button>
              </div>
            </div>
          </div>
          {file.important_text && (
            <div style={{ marginTop: '20px' }}>
              <h4 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px' }}>相关网盘链接</h4>
              <div style={{ 
                background: '#f5f5f5', 
                padding: '15px', 
                borderRadius: '4px', 
                whiteSpace: 'pre-wrap',
                overflowWrap: 'break-word',
                border: '1px solid #e8e8e8'
              }}>
                {file.important_text}
              </div>
            </div>
          )}
        </div>
      );
    }

    // 视频文件
    const videoTypes = ['mp4', 'webm', 'ogg'];
    if (videoTypes.includes(file.file_type?.toLowerCase())) {
      return (
        <div>
          {fileInfoSection}
          {previewImagesSection}
          <div style={{ marginTop: '20px' }}>
            <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px' }}>视频预览</h3>
            <div style={{ textAlign: 'center', maxWidth: '100%', overflow: 'hidden' }}>
              <video 
                controls 
                style={{ maxWidth: '100%', maxHeight: '500px' }} 
                src={`/api/files/${file.id}/download`}
              >
                您的浏览器不支持视频预览
              </video>
              <div style={{ marginTop: '15px' }}>
                <Button type="primary" href={`/api/files/${file.id}/download?download=true`} target="_blank">
                  下载视频
                </Button>
              </div>
            </div>
          </div>
          {file.important_text && (
            <div style={{ marginTop: '20px' }}>
              <h4 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px' }}>相关网盘链接</h4>
              <div style={{ 
                background: '#f5f5f5', 
                padding: '15px', 
                borderRadius: '4px', 
                whiteSpace: 'pre-wrap',
                overflowWrap: 'break-word',
                border: '1px solid #e8e8e8'
              }}>
                {file.important_text}
              </div>
            </div>
          )}
        </div>
      );
    }

    // 其他文件类型
    return (
      <div>
        {fileInfoSection}
        {previewImagesSection}
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <h3 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px', marginBottom: '15px', textAlign: 'left' }}>文件下载</h3>
          <div style={{ 
            padding: '20px', 
            border: '1px dashed #d9d9d9',
            borderRadius: '4px',
            marginBottom: '20px',
            background: '#fafafa'
          }}>
            {getFileIcon(file.file_type)}
            <p style={{ marginTop: '10px' }}>{file.file_name}</p>
          </div>
          <Button type="primary" href={`/api/files/${file.id}/download?download=true`} target="_blank" icon={<UploadOutlined />}>
            下载文件
          </Button>
        </div>
        {file.important_text && (
          <div style={{ marginTop: '20px' }}>
            <h4 style={{ borderBottom: '1px solid #eee', paddingBottom: '8px' }}>相关网盘链接</h4>
            <div style={{ 
              background: '#f5f5f5', 
              padding: '15px', 
              borderRadius: '4px', 
              whiteSpace: 'pre-wrap',
              overflowWrap: 'break-word',
              border: '1px solid #e8e8e8'
            }}>
              {file.important_text}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'file_name',
      key: 'file_name',
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {/* 简介图片缩略图 */}
          {record.preview_images_parsed && record.preview_images_parsed.length > 0 ? (
            <AuthenticatedImage
              width={40}
              height={40}
              src={record.preview_images_parsed[0].startsWith('http') ? record.preview_images_parsed[0] : `/api/admin/preview/${record.preview_images_parsed[0]}`}
              style={{ objectFit: 'cover', borderRadius: 4, width: 40, height: 40 }}
            />
          ) : (
            <div style={{ width: 40, height: 40, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {record.file_type === 'text' ?
                <FileTextOutlined style={{ fontSize: 20, color: '#1890ff' }} /> :
                getFileIcon(record.file_type)}
            </div>
          )}

          <div style={{ flex: 1, minWidth: 0 }}>
            <Text ellipsis={{ tooltip: text }} style={{ display: 'block', maxWidth: 150 }}>{text}</Text>
            <div style={{ marginTop: 4 }}>
              {record.important_text && <Tag color="green" size="small">含网盘链接</Tag>}
              {record.preview_images_parsed && record.preview_images_parsed.length > 0 && (
                <Tag color="blue" size="small">有简介图片({record.preview_images_parsed.length})</Tag>
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size, record) => {
        if (record.file_type === 'text' && !record.file_path) {
          return '网盘链接';
        }
        return size ? formatFileSize(size) : '无大小信息';
      },
    },
    {
      title: '类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type) => <Tag color="blue">{type.toUpperCase()}</Tag>,
    },
    {
      title: '分类',
      dataIndex: ['Category', 'name'],
      key: 'category',
      render: (text) => text || <Text type="secondary">未分类</Text>,
    },
    {
      title: '下载次数',
      dataIndex: 'download_count',
      key: 'download_count',
      render: (count, record) => {
        // 显示实际下载/提取次数
        if (record.file_type === 'text' && !record.file_path) {
          return `${count || 0} 次提取`;
        }
        return `${count || 0} 次下载`;
      },
    },
    {
      title: '上传时间',
      dataIndex: 'upload_time',
      key: 'upload_time',
      render: (date) => formatDate(date),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === true ? 'green' : 'red'}>
          {status === true ? '可用' : '不可用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="预览">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />} 
              onClick={() => handlePreview(record)}
            />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button 
              type="text" 
              size="small" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title={record.status ? "禁用" : "启用"}>
            <Popconfirm
              title={`确定${record.status ? "禁用" : "启用"}此文件?`}
              onConfirm={async () => {
                try {
                  await axios.patch(`/api/admin/files/${record.id}`, {
                    status: !record.status
                  });
                  messageApi.success(`文件${record.status ? "禁用" : "启用"}成功`);
                  // 重新加载文件列表
                  dispatch(getAdminFiles({ 
                    page: currentPage, 
                    limit: pageSize,
                    keyword: searchKeyword,
                    category_id: selectedCategoryId
                  }));
                } catch (error) {
                  messageApi.error(error.response?.data?.message || '操作失败，请重试');
                }
              }}
            >
              <Button 
                type="text" 
                size="small" 
                style={{ color: record.status ? '#faad14' : '#52c41a' }}
              >
                {record.status ? "禁用" : "启用"}
              </Button>
            </Popconfirm>
          </Tooltip>
          
          <Tooltip title="删除">
            <Popconfirm
              title="确定删除此文件?"
              onConfirm={async () => {
                try {
                  const adminToken = localStorage.getItem('adminToken');
                  await axios.delete(`/api/admin/files/${record.id}`, {
                    headers: {
                      'Authorization': `Bearer ${adminToken}`
                    }
                  });
                  messageApi.success('文件删除成功');
                // 重新加载文件列表
                  dispatch(getAdminFiles({ 
                    page: currentPage, 
                    limit: pageSize,
                    keyword: searchKeyword,
                    category_id: selectedCategoryId
                  }));
                } catch (error) {
                  messageApi.error(error.response?.data?.message || '删除失败，请重试');
                }
              }}
            >
              <Button type="text" danger size="small" icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 上传前检查
  const beforeUpload = (file) => {
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      messageApi.error('文件大小不能超过50MB!');
    }
    return isLt50M;
  };

  const uploadProps = {
    name: 'file',
    beforeUpload,
    fileList,
    onChange({ fileList }) {
      setFileList(fileList);
    },
    customRequest: ({ file, onSuccess }) => {
      // 不执行实际上传，只是将文件添加到fileList中
      setTimeout(() => {
        onSuccess("ok", null);
      }, 0);
    },
    action: null,
    multiple: false,
    maxCount: 1
  };

  // 简介图片上传前检查
  const beforePreviewUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      messageApi.error('简介图片只能上传图片文件!');
      return false;
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      messageApi.error('简介图片大小不能超过10MB!');
      return false;
    }
    return true;
  };

  const previewUploadProps = {
    name: 'preview_images',
    beforeUpload: beforePreviewUpload,
    fileList: previewImageList,
    onChange({ fileList }) {
      setPreviewImageList(fileList);
    },
    customRequest: ({ file, onSuccess }) => {
      // 不执行实际上传，只是将文件添加到fileList中
      setTimeout(() => {
        onSuccess("ok", null);
      }, 0);
    },
    action: null,
    multiple: true,
    maxCount: 5,
    listType: "picture-card",
    accept: "image/*"
  };

  // 编辑时的简介图片上传配置
  const editPreviewUploadProps = {
    name: 'preview_images',
    beforeUpload: beforePreviewUpload,
    fileList: editPreviewImageList,
    onChange({ fileList }) {
      setEditPreviewImageList(fileList);
    },
    customRequest: ({ file, onSuccess }) => {
      // 不执行实际上传，只是将文件添加到fileList中
      setTimeout(() => {
        onSuccess("ok", null);
      }, 0);
    },
    action: null,
    multiple: true,
    maxCount: 5,
    listType: "picture-card",
    accept: "image/*"
  };

  // 切换上传模式
  const handleSwitchUploadMode = (checked) => {
    setIsTextOnly(checked);
    if (checked) {
      setFileList([]);
    }

    // 重置表单验证状态
    uploadForm.validateFields(['file']);
  };

  // 获取文件图标
  const getFileTypeIcon = (fileType) => {
    if (fileType === 'text' && !fileType.includes('.')) {
      return <FileTextOutlined style={{ fontSize: 20, color: '#1890ff' }} />;
    }
    return getFileIcon(fileType);
  };

  return (
    <div>
      <Title level={2}>文件管理</Title>
      <Text type="secondary" style={{ marginBottom: 20, display: 'block' }}>
        管理系统中的所有文件资料，包括上传、编辑和删除等操作。
      </Text>

      <Card style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16, display: 'flex', gap: 16 }}>
          <Input
            placeholder="搜索文件名称"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 250 }}
            prefix={<SearchOutlined />}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="选择分类"
            style={{ width: 200 }}
            allowClear
            loading={loadingCategories}
            value={selectedCategoryId}
            onChange={setSelectedCategoryId}
          >
            {categories.map((category) => (
              <Option key={category.id} value={category.id}>
                {category.name}
              </Option>
            ))}
          </Select>
          
          <Button type="primary" onClick={handleSearch}>
            搜索
          </Button>
          
          <Button icon={<ReloadOutlined />} onClick={handleResetFilter}>
            重置
          </Button>
        </div>

        <Space style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setUploadModalVisible(true);
              setIsTextOnly(false);
              uploadForm.resetFields();
              setFileList([]);
              setPreviewImageList([]);
            }}
          >
            上传文件
          </Button>

          <Button
            type="default"
            icon={<SyncOutlined />}
            onClick={async () => {
              setSyncModalVisible(true);
              await fetchXianyuSettings();
            }}
          >
            同步闲鱼商品
          </Button>
        </Space>

        <Spin spinning={loadingFiles}>
          <Table
            columns={columns}
            dataSource={files.map(file => ({ ...file, key: file.id }))}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: totalFiles,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: (total) => `共 ${total} 个文件`,
            }}
          />
        </Spin>
      </Card>

      {/* 上传文件弹窗 */}
      <Modal
        title="上传文件/网盘链接"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setIsTextOnly(false);
          setFileList([]);
          setPreviewImageList([]);
        }}
        footer={null}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={(values) => {
            console.log('表单提交的值:', values);
            handleFileUpload(values);
          }}
        >
          {/* 切换上传模式 */}
          <Form.Item label="上传模式">
            <Space align="center">
              <span>文件上传</span>
              <Switch 
                checked={isTextOnly} 
                onChange={handleSwitchUploadMode}
              />
              <span>网盘链接</span>
            </Space>
            {isTextOnly && (
              <Alert 
                message="选择网盘链接模式后，将只上传文字内容，不需要上传文件" 
                type="info" 
                showIcon 
                style={{ marginTop: 8 }}
              />
            )}
          </Form.Item>

          <Form.Item
            name="file"
            label="选择文件"
            rules={[{ required: !isTextOnly, message: '请选择要上传的文件' }]}
            hidden={isTextOnly}
          >
            <Upload 
              {...uploadProps}
              disabled={isTextOnly}
            >
              <Button icon={<UploadOutlined />} disabled={isTextOnly}>选择文件</Button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="important_text"
            label="网盘链接"
            rules={[{ required: isTextOnly, message: '请输入网盘链接' }]}
            hidden={!isTextOnly}
          >
            <TextArea rows={6} placeholder="请输入需要上传的网盘链接内容" />
          </Form.Item>

          <Form.Item
            name="preview_images"
            label="简介图片"
            extra="可上传最多5张图片，支持JPG、PNG、GIF、WEBP格式，单张图片不超过10MB"
          >
            <Upload {...previewUploadProps}>
              {previewImageList.length >= 5 ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传简介图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            name="filename"
            label="文件名称"
            rules={[{ required: true, message: '请输入文件名称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="category_id"
            label="所属分类"
          >
            <Select placeholder="选择分类">
              {categories.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="文件描述"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              {isTextOnly ? '上传网盘链接' : '上传文件'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文件预览弹窗 */}
      <Modal
        title={`文件预览 - ${previewFile?.file_name || ''}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={null}
        width={window.innerWidth > 1200 ? 800 : '80%'}
        styles={{ 
          body: { 
            maxHeight: 'calc(90vh - 80px)', 
            overflow: 'auto', 
            padding: '16px',
            scrollbarWidth: 'thin',
            scrollbarColor: '#d9d9d9 #f5f5f5'
          }
        }}
        style={{ top: 60 }}
      >
        {previewFile && renderPreviewContent(previewFile)}
      </Modal>

      {/* 编辑文件弹窗 */}
      <Modal
        title="编辑文件信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setFileList([]);
          setEditPreviewImageList([]);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="file_name"
            label="文件名称"
            rules={[{ required: true, message: '请输入文件名称' }]}
          >
            <Input />
          </Form.Item>

          {/* 对于非网盘类资料，显示文件重新上传选项 */}
          {editingFile && !(editingFile.file_type === 'text' && !editingFile.file_path) && (
            <Form.Item
              label="重新上传文件"
              extra="如需替换文件，请选择新文件上传，否则保持原文件"
            >
              <Upload {...editUploadProps}>
                <Button icon={<UploadOutlined />}>选择新文件</Button>
              </Upload>
              {editingFile.file_path && (
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">当前文件: {editingFile.file_name}</Text>
                  <br />
                  <Text type="secondary">类型: {editingFile.file_type?.toUpperCase()}</Text>
                  {editingFile.file_size > 0 && (
                    <>
                      <br />
                      <Text type="secondary">大小: {formatFileSize(editingFile.file_size)}</Text>
                    </>
                  )}
                </div>
              )}
            </Form.Item>
          )}

          <Form.Item
            name="description"
            label="文件描述"
          >
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item
            label="简介图片"
            extra="可上传最多5张图片，支持JPG、PNG、GIF、WEBP格式，单张图片不超过10MB。删除现有图片后点击保存即可删除。"
          >
            <Upload {...editPreviewUploadProps}>
              {editPreviewImageList.length >= 5 ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传简介图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item
            name="category_id"
            label="所属分类"
          >
            <Select placeholder="选择分类" allowClear>
              {categories.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {(editingFile?.file_type === 'text' || editingFile?.important_text) && (
            <Form.Item
              name="important_text"
              label="网盘链接"
            >
              <Input.TextArea rows={6} />
            </Form.Item>
          )}

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="可用" unCheckedChildren="不可用" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              保存修改
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 同步闲鱼商品弹窗 */}
      <Modal
        title="同步闲鱼商品"
        open={syncModalVisible}
        onCancel={() => {
          setSyncModalVisible(false);
          syncForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="同步说明"
          description={
            <div>
              {xianyuSettings.has_cookies ? (
                <div>
                  <p>✅ 已保存闲鱼Cookie，可以直接同步</p>
                  <p>💡 如需更新Cookie，请在下方输入新的Cookie值</p>
                </div>
              ) : (
                <div>
                  <p>1. 请先登录闲鱼网页版获取Cookie</p>
                  <p>2. 按F12打开开发者工具，在Network标签页中找到任意请求</p>
                  <p>3. 复制Request Headers中的完整Cookie值</p>
                </div>
              )}
              <p>4. 同步过程可能需要几分钟，请耐心等待</p>
            </div>
          }
          type={xianyuSettings.has_cookies ? "success" : "info"}
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={syncForm}
          layout="vertical"
          onFinish={handleSyncXianyu}
        >
          <Form.Item
            name="cookies_str"
            label={xianyuSettings.has_cookies ? "闲鱼Cookie（可选，留空使用已保存的Cookie）" : "闲鱼Cookie"}
            rules={xianyuSettings.has_cookies ? [] : [{ required: true, message: '请输入闲鱼Cookie' }]}
          >
            <TextArea
              rows={6}
              placeholder={xianyuSettings.has_cookies ?
                "留空使用已保存的Cookie，或输入新的Cookie进行更新..." :
                "请粘贴从浏览器开发者工具中复制的完整Cookie字符串..."
              }
            />
          </Form.Item>



          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={syncing}
              block
            >
              {syncing ? '正在同步...' : '开始同步'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Files; 