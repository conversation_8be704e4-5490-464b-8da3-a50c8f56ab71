const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const { spawn } = require('child_process');
const { File, Category, Setting } = require('../models');
const { Op } = require('sequelize');

// 定义文件类型映射
const getFileTypeFolder = (fileExt) => {
  // 转换为小写
  const ext = fileExt.toLowerCase();
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return 'images';
  }
  
  // 文档文件
  if (['doc', 'docx', 'rtf'].includes(ext)) {
    return 'word';
  }
  
  // PDF文件
  if (ext === 'pdf') {
    return 'pdf';
  }
  
  // 表格文件
  if (['xls', 'xlsx', 'csv'].includes(ext)) {
    return 'excel';
  }
  
  // 音频文件
  if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
    return 'audio';
  }
  
  // 视频文件
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'].includes(ext)) {
    return 'video';
  }
  
  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'archives';
  }
  
  // 代码文件
  if (['js', 'java', 'py', 'c', 'cpp', 'cs', 'php', 'html', 'css'].includes(ext)) {
    return 'code';
  }
  
  // 其他文件放入other文件夹
  return 'other';
};

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 获取主上传目录
    const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
    
    // 确保基本上传目录存在
    if (!fs.existsSync(baseUploadPath)) {
      fs.mkdirSync(baseUploadPath, { recursive: true });
    }
    
    // 获取文件扩展名
    const fileExt = path.extname(file.originalname).toLowerCase().replace('.', '');
    
    // 根据文件类型确定子文件夹
    const typeFolder = getFileTypeFolder(fileExt);
    const typePath = path.join(baseUploadPath, typeFolder);
    
    // 确保子文件夹存在
    if (!fs.existsSync(typePath)) {
      fs.mkdirSync(typePath, { recursive: true });
    }
    
    // 保存到对应的子文件夹
    cb(null, typePath);
  },
  filename: (req, file, cb) => {
    // 处理原始文件名的编码问题
    let originalName = file.originalname;
    
    try {
      // 如果是Buffer，转换为UTF-8字符串
      if (Buffer.isBuffer(originalName)) {
        originalName = originalName.toString('utf8');
      }
      
      // 确保文件扩展名正确
      const fileExt = path.extname(originalName);
      
    // 生成唯一文件名
      const uniqueFileName = `${Date.now()}-${uuidv4()}${fileExt}`;
      console.log('生成的文件名:', uniqueFileName);
      
    cb(null, uniqueFileName);
    } catch (error) {
      console.error('处理文件名出错:', error);
      // 出错时使用安全的备用文件名
      const safeFileName = `${Date.now()}-${uuidv4()}.unknown`;
      cb(null, safeFileName);
    }
  }
});

// 简介图片存储配置
const previewStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
    const previewPath = path.join(baseUploadPath, 'preview');

    // 确保preview文件夹存在
    if (!fs.existsSync(previewPath)) {
      fs.mkdirSync(previewPath, { recursive: true });
    }

    cb(null, previewPath);
  },
  filename: (req, file, cb) => {
    try {
      const fileExt = path.extname(file.originalname);
      const uniqueFileName = `preview-${Date.now()}-${uuidv4()}${fileExt}`;
      cb(null, uniqueFileName);
    } catch (error) {
      console.error('处理简介图片文件名出错:', error);
      const safeFileName = `preview-${Date.now()}-${uuidv4()}.jpg`;
      cb(null, safeFileName);
    }
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 限制文件大小（100MB）
  fileFilter: (req, file, cb) => {
    // 处理原始文件名编码
    try {
      // 对file.originalname进行解码，确保中文等字符正确显示
      if (Buffer.isBuffer(file.originalname)) {
        file.originalname = file.originalname.toString('utf8');
      } else if (typeof file.originalname !== 'string') {
        file.originalname = String(file.originalname);
      }
    cb(null, true);
    } catch (error) {
      console.error('文件过滤错误:', error);
      cb(null, true); // 继续允许文件上传
    }
  }
});

// 配置多文件上传：主文件 + 简介图片
const uploadFields = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const baseUploadPath = process.env.UPLOAD_PATH || './uploads';

      if (file.fieldname === 'preview_images') {
        // 简介图片存储到preview文件夹
        const previewPath = path.join(baseUploadPath, 'preview');
        if (!fs.existsSync(previewPath)) {
          fs.mkdirSync(previewPath, { recursive: true });
        }
        cb(null, previewPath);
      } else {
        // 主文件按原来的逻辑存储
        if (!fs.existsSync(baseUploadPath)) {
          fs.mkdirSync(baseUploadPath, { recursive: true });
        }

        const fileExt = path.extname(file.originalname).toLowerCase().replace('.', '');
        const typeFolder = getFileTypeFolder(fileExt);
        const typePath = path.join(baseUploadPath, typeFolder);

        if (!fs.existsSync(typePath)) {
          fs.mkdirSync(typePath, { recursive: true });
        }

        cb(null, typePath);
      }
    },
    filename: (req, file, cb) => {
      try {
        let originalName = file.originalname;

        if (Buffer.isBuffer(originalName)) {
          originalName = originalName.toString('utf8');
        }

        const fileExt = path.extname(originalName);

        if (file.fieldname === 'preview_images') {
          // 简介图片文件名
          const uniqueFileName = `preview-${Date.now()}-${uuidv4()}${fileExt}`;
          cb(null, uniqueFileName);
        } else {
          // 主文件文件名
          const uniqueFileName = `${Date.now()}-${uuidv4()}${fileExt}`;
          cb(null, uniqueFileName);
        }
      } catch (error) {
        console.error('处理文件名出错:', error);
        const safeFileName = `${Date.now()}-${uuidv4()}.unknown`;
        cb(null, safeFileName);
      }
    }
  }),
  limits: { fileSize: 100 * 1024 * 1024 }, // 限制文件大小（100MB）
  fileFilter: (req, file, cb) => {
    try {
      if (Buffer.isBuffer(file.originalname)) {
        file.originalname = file.originalname.toString('utf8');
      } else if (typeof file.originalname !== 'string') {
        file.originalname = String(file.originalname);
      }

      if (file.fieldname === 'preview_images') {
        // 简介图片只允许图片文件
        const allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        const fileExt = path.extname(file.originalname).toLowerCase().replace('.', '');

        if (allowedTypes.includes(fileExt)) {
          cb(null, true);
        } else {
          cb(new Error('简介图片只支持 JPG, PNG, GIF, WEBP 格式'), false);
        }
      } else {
        // 主文件允许所有类型
        cb(null, true);
      }
    } catch (error) {
      console.error('文件过滤错误:', error);
      cb(null, true);
    }
  }
}).fields([
  { name: 'file', maxCount: 1 },
  { name: 'preview_images', maxCount: 5 }
]);

/**
 * 上传文件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const uploadFile = (req, res) => {
  // 如果有网盘链接但没有文件，直接创建只包含文字的记录
  if (req.body.important_text && !req.files && !req.file) {
    handleTextOnlyUpload(req, res);
    return;
  }

  // 使用多文件上传处理
  uploadFields(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message || '文件上传失败'
      });
    }

    try {
      // 即使没有文件，但有网盘链接，也允许上传
      const { description, category_id, filename: customFilename, important_text } = req.body;

      // 处理简介图片
      let previewImagePaths = [];
      if (req.files && req.files.preview_images) {
        previewImagePaths = req.files.preview_images.map(file => {
          // 只存储文件名，因为API路由已经包含了preview路径
          return file.filename;
        });
      }

      // 处理有文件的情况
      if (req.files && req.files.file && req.files.file[0]) {
        const mainFile = req.files.file[0];
        const { originalname, filename, path: filePath, size } = mainFile;

        // 处理文件名编码问题，确保中文等字符正确显示
        // 优先使用表单传递的自定义文件名，否则使用原始文件名
        let displayFileName = customFilename || originalname;
        
        // 确保文件名是正确编码的字符串
        if (Buffer.isBuffer(displayFileName)) {
          displayFileName = displayFileName.toString('utf8');
        } else if (typeof displayFileName !== 'string') {
          displayFileName = String(displayFileName);
        }
        
        console.log('处理后的文件名:', displayFileName);

        // 获取文件扩展名
        const fileExt = path.extname(originalname).toLowerCase().replace('.', '');
        
        // 获取相对于uploads目录的路径
        const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
        const relativePath = path.relative(baseUploadPath, filePath);
        
        // 创建文件记录
        const file = await File.create({
          file_name: displayFileName,
          file_path: relativePath, // 保存相对路径，便于后续访问
          file_size: Math.ceil(size / 1024), // 转换为KB
          file_type: fileExt,
          description: description || '',
          important_text: important_text || null,
          preview_images: previewImagePaths.length > 0 ? JSON.stringify(previewImagePaths) : null,
          category_id: category_id || null,
          status: true // 确保文件状态为可用
        });

        return res.json({
          success: true,
          message: '文件上传成功',
          data: { file }
        });
      } else {
        // 没有文件但也没有网盘链接，返回错误
        if (!important_text) {
          return res.status(400).json({ success: false, message: '未选择文件且没有输入网盘链接' });
        }
        
        // 处理仅有文字的情况
        handleTextOnlyUpload(req, res);
      }
    } catch (error) {
      console.error('上传文件错误:', error);
      return res.status(500).json({ success: false, message: '服务器错误' });
    }
  });
};

/**
 * 处理仅文字信息的上传
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const handleTextOnlyUpload = async (req, res) => {
  try {
    const { description, category_id, filename: customFilename, important_text } = req.body;

    if (!important_text) {
      return res.status(400).json({ success: false, message: '网盘链接不能为空' });
    }

    // 处理简介图片（即使是纯文本也可能有简介图片）
    let previewImagePaths = [];
    if (req.files && req.files.preview_images) {
      previewImagePaths = req.files.preview_images.map(file => {
        // 只存储文件名，因为API路由已经包含了preview路径
        return file.filename;
      });
    }

    // 使用默认文件名或自定义文件名
    let displayFileName = customFilename || '网盘链接';

    // 创建文件记录（无实际文件）
    const file = await File.create({
      file_name: displayFileName,
      file_path: null, // 无文件路径
      file_size: null, // 无文件大小
      file_type: 'text', // 类型标记为text
      description: description || '',
      important_text: important_text,
      preview_images: previewImagePaths.length > 0 ? JSON.stringify(previewImagePaths) : null,
      category_id: category_id || null,
      status: true
    });

    return res.json({
      success: true,
      message: '网盘链接上传成功',
      data: { file }
    });
  } catch (error) {
    console.error('上传网盘链接错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取管理员文件列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getAdminFiles = async (req, res) => {
  try {
    const { page = 1, limit = 10, category_id, keyword, status } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    if (category_id) {
      where.category_id = category_id;
    }
    if (keyword) {
      where.file_name = { [Op.like]: `%${keyword}%` };
    }
    if (status !== undefined) {
      where.status = status === 'true';
    }

    // 获取文件列表
    const { count, rows } = await File.findAndCountAll({
      where,
      include: [{ model: Category, attributes: ['id', 'name'] }],
      order: [['upload_time', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理结果，确保为空的字段有默认值
    const processedFiles = rows.map(file => {
      const fileObj = file.toJSON();
      return {
        ...fileObj,
        file_size: fileObj.file_size || 0, // 确保file_size不为null
        file_path: fileObj.file_path || '', // 确保file_path不为null
        preview_images_parsed: fileObj.preview_images ? JSON.parse(fileObj.preview_images) : [] // 解析简介图片JSON
      };
    });

    return res.json({
      success: true,
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        files: processedFiles
      }
    });
  } catch (error) {
    console.error('获取管理员文件列表错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取管理员文件详情
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getAdminFileDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询文件
    const file = await File.findOne({
      where: { id },
      include: [{ model: Category, attributes: ['id', 'name'] }]
    });
    
    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在' });
    }
    
    // 处理结果，确保为空的字段有默认值
    const fileObj = file.toJSON();
    const processedFile = {
      ...fileObj,
      file_size: fileObj.file_size || 0, // 确保file_size不为null
      file_path: fileObj.file_path || '', // 确保file_path不为null
      preview_images_parsed: fileObj.preview_images ? JSON.parse(fileObj.preview_images) : [] // 解析简介图片JSON
    };
    
    return res.json({
      success: true,
      data: { file: processedFile }
    });
  } catch (error) {
    console.error('获取文件详情错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 更新文件信息（支持简介图片）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateFile = (req, res) => {
  // 使用多文件上传处理简介图片更新
  uploadFields(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        message: err.message || '文件更新失败'
      });
    }

    try {
      const { id } = req.params;
      const { file_name, description, category_id, status, important_text, keep_preview_images } = req.body;

      // 查询文件
      const file = await File.findByPk(id);

      if (!file) {
        return res.status(404).json({ success: false, message: '文件不存在' });
      }

      // 处理简介图片
      let previewImagePaths = [];
      let shouldUpdatePreviewImages = false;

      // 如果要保留原有简介图片
      if (keep_preview_images === 'true' && file.preview_images) {
        try {
          previewImagePaths = JSON.parse(file.preview_images);
        } catch (e) {
          previewImagePaths = [];
        }
      }

      // 如果有新的简介图片上传
      if (req.files && req.files.preview_images) {
        shouldUpdatePreviewImages = true;
        const newPreviewPaths = req.files.preview_images.map(file => {
          // 只存储文件名，因为API路由已经包含了preview路径
          return file.filename;
        });

        // 如果不保留原有图片，则替换；否则追加
        if (keep_preview_images !== 'true') {
          previewImagePaths = newPreviewPaths;
        } else {
          previewImagePaths = [...previewImagePaths, ...newPreviewPaths];
        }
      } else if (keep_preview_images === 'true') {
        // 如果没有新图片但要保留原有图片，不更新preview_images字段
        shouldUpdatePreviewImages = false;
      } else if (keep_preview_images === 'false') {
        // 明确要求清空图片
        shouldUpdatePreviewImages = true;
        previewImagePaths = [];
      }

      // 准备更新数据
      const updateData = {
        file_name: file_name || file.file_name,
        description: description !== undefined ? description : file.description,
        category_id: category_id !== undefined ? category_id : file.category_id,
        status: status !== undefined ? Boolean(status) : file.status,
        important_text: important_text !== undefined ? important_text : file.important_text
      };

      // 只有在需要更新预览图片时才添加该字段
      if (shouldUpdatePreviewImages) {
        updateData.preview_images = previewImagePaths.length > 0 ? JSON.stringify(previewImagePaths) : null;
      }

      // 更新信息
      await file.update(updateData);

      // 获取更新后的文件（包含分类信息）
      const updatedFile = await File.findOne({
        where: { id },
        include: [{ model: Category, attributes: ['id', 'name'] }]
      });

      return res.json({
        success: true,
        message: '文件信息更新成功',
        data: { file: updatedFile }
      });
    } catch (error) {
      console.error('更新文件信息错误:', error);
      return res.status(500).json({ success: false, message: '服务器错误' });
    }
  });
};

/**
 * 更新文件内容（替换文件）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const updateFileContent = (req, res) => {
  // 使用与上传类似的处理
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ 
        success: false, 
        message: err.message || '文件更新失败'
      });
    }

    try {
      const { id } = req.params;
      const { file_name, description, category_id, status, important_text } = req.body;
      
      // 查询原始文件
      const originalFile = await File.findByPk(id);
      
      if (!originalFile) {
        return res.status(404).json({ success: false, message: '文件不存在' });
      }
      
      // 如果没有上传新文件，则返回错误
      if (!req.file) {
        return res.status(400).json({ success: false, message: '未选择新文件' });
      }

      // 处理旧文件的删除
      if (originalFile.file_path) {
        try {
          // 获取基础上传路径
          const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
          let oldFilePath;
          
          // 处理文件路径 - 兼容新旧格式
          if (originalFile.file_path.includes(path.sep)) {
            oldFilePath = path.resolve(baseUploadPath, originalFile.file_path);
          } else {
            oldFilePath = path.resolve(baseUploadPath, originalFile.file_path);
            
            // 如果旧格式路径不存在，尝试查找该文件的位置
            if (!fs.existsSync(oldFilePath)) {
              const typeFolders = ['images', 'word', 'pdf', 'excel', 'audio', 'video', 'archives', 'code', 'other'];
              
              for (const folder of typeFolders) {
                const possiblePath = path.resolve(baseUploadPath, folder, originalFile.file_path);
                if (fs.existsSync(possiblePath)) {
                  oldFilePath = possiblePath;
                  break;
                }
              }
            }
          }
          
          // 尝试删除旧文件
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
            console.log(`成功删除旧文件: ${oldFilePath}`);
          }
        } catch (fsError) {
          console.error('删除旧文件错误:', fsError);
          // 继续处理，不因为删除旧文件失败而中断流程
        }
      }
      
      // 处理新上传的文件
      const { originalname, filename, path: filePath, size } = req.file;
      
      // 处理文件名编码问题，确保中文等字符正确显示
      let displayFileName = file_name || originalname;
      
      // 确保文件名是正确编码的字符串
      if (Buffer.isBuffer(displayFileName)) {
        displayFileName = displayFileName.toString('utf8');
      } else if (typeof displayFileName !== 'string') {
        displayFileName = String(displayFileName);
      }
      
      // 获取文件扩展名
      const fileExt = path.extname(originalname).toLowerCase().replace('.', '');
      
      // 获取相对于uploads目录的路径
      const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
      const relativePath = path.relative(baseUploadPath, filePath);
      
      // 更新文件记录
      await originalFile.update({
        file_name: displayFileName,
        file_path: relativePath, // 保存相对路径
        file_size: Math.ceil(size / 1024), // 转换为KB
        file_type: fileExt,
        description: description || originalFile.description,
        important_text: important_text !== undefined ? important_text : originalFile.important_text,
        category_id: category_id !== undefined ? category_id : originalFile.category_id,
        status: status !== undefined ? (status === 'true' || status === true) : originalFile.status
      });
      
      // 获取完整的更新后文件记录（包含分类信息）
      const updatedFile = await File.findOne({
        where: { id },
        include: [{ model: Category, attributes: ['id', 'name'] }]
      });
      
      return res.json({
        success: true,
        message: '文件更新成功',
        data: { file: updatedFile }
      });
    } catch (error) {
      console.error('更新文件内容错误:', error);
      return res.status(500).json({ success: false, message: '服务器错误' });
    }
  });
};

/**
 * 删除文件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteFile = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询文件
    const file = await File.findByPk(id);
    
    if (!file) {
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    // 移除检查文件是否已被使用的限制，允许管理员删除已被使用的文件
    // 注意：这可能会导致已提取但未下载的用户无法访问文件

    // 删除简介图片
    if (file.preview_images) {
      try {
        const previewImages = JSON.parse(file.preview_images);
        const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
        const previewPath = path.join(baseUploadPath, 'preview');

        previewImages.forEach(imageName => {
          // 处理不同格式的图片路径
          let imageFileName = imageName;
          if (imageName.includes('preview/') || imageName.includes('preview\\')) {
            // 旧格式：包含preview/前缀
            imageFileName = imageName.replace(/^preview[\\\/]/, '');
          }

          const imageFilePath = path.join(previewPath, imageFileName);

          try {
            if (fs.existsSync(imageFilePath)) {
              fs.unlinkSync(imageFilePath);
              console.log(`成功删除简介图片: ${imageFilePath}`);
            } else {
              console.warn(`简介图片不存在，无法删除: ${imageFilePath}`);
            }
          } catch (fsError) {
            console.error('删除简介图片错误:', fsError);
          }
        });
      } catch (parseError) {
        console.error('解析简介图片路径错误:', parseError);
      }
    }

    // 如果不是纯文本记录(即有实际物理文件)，才尝试删除物理文件
    if (file.file_path) {
      // 获取基础上传路径
      const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
      let filePath;
      
      // 处理文件路径 - 兼容新旧格式
      if (file.file_path.includes(path.sep)) {
        // 新格式：相对路径，包含子文件夹
        filePath = path.resolve(baseUploadPath, file.file_path);
      } else {
        // 旧格式：只有文件名，位于根目录
        filePath = path.resolve(baseUploadPath, file.file_path);
        
        // 如果旧格式路径不存在，尝试查找该文件的位置
        if (!fs.existsSync(filePath)) {
          console.log(`旧格式文件路径不存在，尝试查找文件: ${file.file_path}`);
          
          // 获取所有可能的文件类型文件夹
          const typeFolders = ['images', 'word', 'pdf', 'excel', 'audio', 'video', 'archives', 'code', 'other'];
          
          // 在每个文件夹中查找
          for (const folder of typeFolders) {
            const possiblePath = path.resolve(baseUploadPath, folder, file.file_path);
            if (fs.existsSync(possiblePath)) {
              filePath = possiblePath;
              console.log(`文件找到于: ${filePath}`);
              break;
            }
          }
        }
      }
      
      // 尝试删除物理文件
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`成功删除文件: ${filePath}`);
        } else {
          console.warn(`文件不存在，无法删除: ${filePath}`);
        }
      } catch (fsError) {
        console.error('删除物理文件错误:', fsError);
        // 继续返回成功，因为数据库记录已删除
      }
    } else {
      console.log('纯文本记录，无需删除物理文件');
    }
    
    // 删除数据库记录
    await file.destroy();
    
    return res.json({
      success: true,
      message: '文件删除成功'
    });
  } catch (error) {
    console.error('删除文件错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 获取简介图片
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getPreviewImage = async (req, res) => {
  try {
    const { filename } = req.params;

    // 构建文件路径
    const baseUploadPath = process.env.UPLOAD_PATH || './uploads';
    const filePath = path.join(baseUploadPath, 'preview', filename);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '简介图片不存在' });
    }

    // 获取文件扩展名来设置正确的Content-Type
    const fileExt = path.extname(filename).toLowerCase();
    let contentType = 'image/jpeg'; // 默认

    switch (fileExt) {
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.jpg':
      case '.jpeg':
      default:
        contentType = 'image/jpeg';
        break;
    }

    // 设置响应头
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天

    // 创建可读流并发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('获取简介图片错误:', error);
    return res.status(500).json({ success: false, message: '服务器错误' });
  }
};

/**
 * 同步闲鱼商品信息到数据库
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const syncXianyuItems = async (req, res) => {
  try {
    let { cookies_str, max_items = 50 } = req.body;

    // 如果没有提供cookies_str，尝试从数据库获取
    if (!cookies_str) {
      cookies_str = await Setting.getXianyuCookies();
      if (!cookies_str) {
        return res.status(400).json({
          success: false,
          message: '请先在设置中配置闲鱼Cookie或在请求中提供Cookie字符串'
        });
      }
    }

    // 如果没有提供max_items，从数据库获取默认值
    if (!req.body.max_items) {
      max_items = await Setting.getXianyuMaxItems();
    }

    // 调用Python脚本进行同步
    const pythonScriptPath = path.join(__dirname, '../../XianYu/sync_xianyu_items.py');

    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', [pythonScriptPath, cookies_str, max_items.toString()]);

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', async (code) => {
        try {
          if (code !== 0) {
            console.error('Python脚本执行失败:', stderr);
            return res.status(500).json({
              success: false,
              message: `同步失败: ${stderr || '未知错误'}`
            });
          }

          // 解析Python脚本返回的结果
          const syncResult = JSON.parse(stdout);

          if (!syncResult.success) {
            return res.status(400).json(syncResult);
          }

          // 将闲鱼商品数据保存到数据库
          const savedItems = [];
          const errors = [];

          // 辅助函数：从商品描述中提取真正的商品名称
          const extractProductName = (description, fallbackTitle) => {
            if (!description) {
              return fallbackTitle || '未知商品';
            }

            // 获取描述的第一行作为商品名称
            const firstLine = description.split('\n')[0].trim();

            // 如果第一行为空或太短，使用fallback标题
            if (!firstLine || firstLine.length < 2) {
              return fallbackTitle || '未知商品';
            }

            // 移除可能的特殊字符和多余空格
            const cleanName = firstLine.replace(/[【】\[\]]/g, '').trim();

            return cleanName || fallbackTitle || '未知商品';
          };

          for (const item of syncResult.data.items) {
            try {
              // 从商品描述中提取真正的商品名称
              const productName = extractProductName(item.description, item.title);

              // 检查是否已经存在相同的闲鱼商品（通过ID检查）
              const existingFile = await File.findOne({
                where: {
                  id: item.itemId
                }
              });

              if (existingFile) {
                // 更新现有记录
                await existingFile.update({
                  file_name: productName, // 使用从描述中提取的商品名称
                  description: item.description,
                  preview_images: item.images && item.images.length > 0 ? JSON.stringify(item.images) : null,
                  file_path: null, // 网盘链接类资料不设置file_path
                  file_type: 'text', // 设置为text类型（网盘链接类资料）
                  // important_text 保持不变，由管理员手动填写网盘链接
                  status: item.status === '0' ? 1 : 0
                });
                savedItems.push(existingFile);
              } else {
                // 创建新记录，使用闲鱼商品ID作为主键
                const newFile = await File.create({
                  id: item.itemId, // 使用闲鱼商品ID作为主键
                  file_name: productName, // 使用从描述中提取的商品名称
                  file_path: null, // 网盘链接类资料不设置file_path
                  file_size: null,
                  file_type: 'text', // 设置为text类型（网盘链接类资料）
                  description: item.description,
                  important_text: null, // 留空，由管理员手动填写网盘链接
                  preview_images: item.images && item.images.length > 0 ? JSON.stringify(item.images) : null,
                  category_id: null,
                  status: item.status === '0' ? 1 : 0 // 闲鱼状态0表示在售，转换为数据库的1表示可用
                });
                savedItems.push(newFile);
              }
            } catch (error) {
              console.error(`保存商品 ${item.itemId} 失败:`, error);
              errors.push(`保存商品 ${item.itemId} 失败: ${error.message}`);
            }
          }

          return res.json({
            success: true,
            message: `同步完成，成功处理 ${savedItems.length} 个商品`,
            data: {
              total: syncResult.data.total,
              synced: syncResult.data.synced,
              saved: savedItems.length,
              errors: [...syncResult.data.errors, ...errors]
            }
          });

        } catch (error) {
          console.error('处理同步结果失败:', error);
          return res.status(500).json({
            success: false,
            message: `处理同步结果失败: ${error.message}`
          });
        }
      });
    });

  } catch (error) {
    console.error('同步闲鱼商品错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

module.exports = {
  uploadFile,
  getAdminFiles,
  getAdminFileDetails,
  updateFile,
  updateFileContent,
  deleteFile,
  getPreviewImage,
  syncXianyuItems
};